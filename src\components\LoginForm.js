import React, { useState } from 'react';
import './LoginForm.css';

const LoginForm = ({ onLogin }) => {
  const [name, setName] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      onLogin({ name: name.trim() });
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <h1>ApnaCollege DSA</h1>
        <p>Learn Data Structures & Algorithms</p>

        <form onSubmit={handleSubmit}>
          <input
            type="text"
            placeholder="Enter your name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
          <button type="submit">Start Learning</button>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;