import React, { useState, useEffect } from 'react';
import './Topics.css';
import { topics } from './topicsData';

const Topics = ({ userEmail, onProgressChange }) => {
	const [doneByKey, setDoneByKey] = useState({});
	const storageKey = `apnacollege_progress_${userEmail || 'guest'}`;

	useEffect(() => {
		try {
			const saved = localStorage.getItem(storageKey);
			setDoneByKey(saved ? JSON.parse(saved) : {});
		} catch {
			setDoneByKey({});
		}
	}, [userEmail, storageKey]);

	useEffect(() => {
		try {
			localStorage.setItem(storageKey, JSON.stringify(doneByKey));
		} catch {
			// ignore
		}
		if (onProgressChange) onProgressChange();
	}, [doneByKey, storageKey, onProgressChange]);

	const toggleDone = (id) => {
		setDoneByKey((prev) => ({ ...prev, [id]: !prev[id] }));
	};

	// Show only first 10 topics for better performance
	const displayTopics = topics.slice(0, 10);

	return (
		<div className="topics-simple">
			<h3>DSA Problems</h3>
			<div className="topics-list">
				{displayTopics.map((topic) => {
					const isDone = !!doneByKey[topic.id];
					return (
						<div key={topic.id} className="topic-item">
							<div className="topic-header">
								<input
									type="checkbox"
									checked={isDone}
									onChange={() => toggleDone(topic.id)}
								/>
								<span className="topic-title">{topic.title}</span>
								<span className={`topic-level ${topic.level.toLowerCase()}`}>
									{topic.level}
								</span>
							</div>
							<div className="topic-links">
								<a href={topic.leetcode} target="_blank" rel="noreferrer">
									LeetCode
								</a>
								<a href={topic.youtube} target="_blank" rel="noreferrer">
									Video
								</a>
								<a href={topic.article} target="_blank" rel="noreferrer">
									Article
								</a>
							</div>
						</div>
					);
				})}
			</div>
			<p className="topics-note">
				Showing {displayTopics.length} of {topics.length} problems
			</p>
		</div>
	);
};

export default Topics;