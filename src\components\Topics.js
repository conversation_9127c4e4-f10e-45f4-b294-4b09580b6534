import React, { useState, useEffect, useCallback } from 'react';
import './Topics.css';
import { topics } from './topicsData';

const QuestionTable = React.memo(({ rows, doneByKey, toggleDone }) => (
	<div className="table-wrap">
		<table className="q-table">
			<thead>
				<tr>
					<th className="chk"></th>
					<th>Question</th>
					<th>Level</th>
					<th>LeetCode</th>
					<th>YouTube</th>
					<th>Article</th>
					<th>Status</th>
				</tr>
			</thead>
			<tbody>
				{rows.map((r, i) => {
					const isDone = !!doneByKey[r.id];
					return (
						<tr key={r.id || i}>
							<td className="chk">
								<input
									type="checkbox"
									checked={isDone}
									onChange={() => toggleDone(r.id)}
								/>
							</td>
							<td>{r.title}</td>
							<td><span className={`level level-${r.level.toLowerCase()}`}>{r.level}</span></td>
							<td><a href={r.leetcode} target="_blank" rel="noreferrer">Link</a></td>
							<td><a href={r.youtube} target="_blank" rel="noreferrer">Video</a></td>
							<td><a href={r.article} target="_blank" rel="noreferrer">Theory</a></td>
							<td>
								<span className={`status ${isDone ? 'done' : 'pending'}`}>
									{isDone ? 'Done' : 'Pending'}
								</span>
							</td>
						</tr>
					);
				})}
			</tbody>
		</table>
	</div>
));

const Topics = ({ userEmail, onProgressChange }) => {
	const [open, setOpen] = useState(new Set(['algorithms']));
	const [doneByKey, setDoneByKey] = useState({});

	const storageKey = `apnacollege_progress_${userEmail || 'guest'}`;

	useEffect(() => {
		try {
			const saved = localStorage.getItem(storageKey);
			setDoneByKey(saved ? JSON.parse(saved) : {});
		} catch {
			setDoneByKey({});
		}
	}, [userEmail, storageKey]);

	useEffect(() => {
		try {
			localStorage.setItem(storageKey, JSON.stringify(doneByKey));
		} catch {
			// ignore
		}
		if (onProgressChange) onProgressChange();
	}, [doneByKey, storageKey, onProgressChange]);

	const toggle = useCallback((id) => {
		setOpen((prev) => {
			const next = new Set(prev);
			next.has(id) ? next.delete(id) : next.add(id);
			return next;
		});
	}, []);

	const toggleDone = useCallback((id) => {
		setDoneByKey((prev) => ({ ...prev, [id]: !prev[id] }));
	}, []);

	// group helpers
	const byGroup = useCallback((g) => topics.filter(t => t.group === g), []);

	return (
		<div className="accordion">
			<section className="acc-item">
				<button className="acc-header" onClick={() => toggle('algorithms')}>
					<span className={`chev ${open.has('algorithms') ? 'open' : ''}`}>▸</span>
					<span className="acc-title">Algorithms</span>
				</button>
				{open.has('algorithms') && (
					<div className="acc-body">
						<h3 className="subhead">Dynamic Programming</h3>
						<QuestionTable rows={byGroup('algorithms.dp')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Graphs</h3>
						<QuestionTable rows={byGroup('algorithms.graphs')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Binary Search</h3>
						<QuestionTable rows={byGroup('algorithms.binarySearch')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Sorting</h3>
						<QuestionTable rows={byGroup('algorithms.sorting')} doneByKey={doneByKey} toggleDone={toggleDone} />
					</div>
				)}
			</section>

			<section className="acc-item">
				<button className="acc-header" onClick={() => toggle('data-structures')}>
					<span className={`chev ${open.has('data-structures') ? 'open' : ''}`}>▸</span>
					<span className="acc-title">Data Structures</span>
				</button>
				{open.has('data-structures') && (
					<div className="acc-body">
						<h3 className="subhead">Arrays</h3>
						<QuestionTable rows={byGroup('ds.arrays')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Linked Lists</h3>
						<QuestionTable rows={byGroup('ds.linkedLists')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Trees</h3>
						<QuestionTable rows={byGroup('ds.trees')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Stacks</h3>
						<QuestionTable rows={byGroup('ds.stacks')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Queues</h3>
						<QuestionTable rows={byGroup('ds.queues')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Hash Tables</h3>
						<QuestionTable rows={byGroup('ds.hash')} doneByKey={doneByKey} toggleDone={toggleDone} />
						<h3 className="subhead">Heaps</h3>
						<QuestionTable rows={byGroup('ds.heap')} doneByKey={doneByKey} toggleDone={toggleDone} />
					</div>
				)}
			</section>
		</div>
	);
};

export default Topics;