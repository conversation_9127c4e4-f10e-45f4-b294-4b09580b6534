import { useState, useEffect } from 'react';
import './Topics.css';
import { topics } from './topicsData';

const Topics = ({ userEmail }) => {
	const [open, setOpen] = useState(new Set(['algorithms']));
	const [done, setDone] = useState({});
	const key = `progress_${userEmail || 'guest'}`;

	useEffect(() => {
		const saved = localStorage.getItem(key);
		if (saved) setDone(JSON.parse(saved));
	}, [key]);

	useEffect(() => {
		localStorage.setItem(key, JSON.stringify(done));
	}, [done, key]);

	const toggle = (id) => {
		const next = new Set(open);
		next.has(id) ? next.delete(id) : next.add(id);
		setOpen(next);
	};

	const toggleDone = (id) => setDone(prev => ({ ...prev, [id]: !prev[id] }));

	const Table = ({ rows }) => (
		<table className="q-table">
			<thead>
				<tr>
					<th></th><th>Question</th><th>Level</th><th>Links</th><th>Status</th>
				</tr>
			</thead>
			<tbody>
				{rows.map(r => (
					<tr key={r.id}>
						<td><input type="checkbox" checked={!!done[r.id]} onChange={() => toggleDone(r.id)} /></td>
						<td>{r.title}</td>
						<td><span className={`level level-${r.level.toLowerCase()}`}>{r.level}</span></td>
						<td>
							<a href={r.leetcode} target="_blank" rel="noreferrer">LC</a> |
							<a href={r.youtube} target="_blank" rel="noreferrer">Video</a> |
							<a href={r.article} target="_blank" rel="noreferrer">Article</a>
						</td>
						<td><span className={`status ${done[r.id] ? 'done' : 'pending'}`}>
							{done[r.id] ? 'Done' : 'Pending'}
						</span></td>
					</tr>
				))}
			</tbody>
		</table>
	);

	const Section = ({ id, title, children }) => (
		<div className="acc-item">
			<button className="acc-header" onClick={() => toggle(id)}>
				<span className={`chev ${open.has(id) ? 'open' : ''}`}>▸</span>
				{title}
			</button>
			{open.has(id) && <div className="acc-body">{children}</div>}
		</div>
	);

	const byGroup = (g) => topics.filter(t => t.group === g);

	return (
		<div className="accordion">
			<Section id="algorithms" title="Algorithms">
				<h3 className="subhead">Dynamic Programming</h3>
				<Table rows={byGroup('algorithms.dp')} />
				<h3 className="subhead">Graphs</h3>
				<Table rows={byGroup('algorithms.graphs')} />
				<h3 className="subhead">Binary Search</h3>
				<Table rows={byGroup('algorithms.binarySearch')} />
				<h3 className="subhead">Sorting</h3>
				<Table rows={byGroup('algorithms.sorting')} />
			</Section>

			<Section id="data-structures" title="Data Structures">
				<h3 className="subhead">Arrays</h3>
				<Table rows={byGroup('ds.arrays')} />
				<h3 className="subhead">Linked Lists</h3>
				<Table rows={byGroup('ds.linkedLists')} />
				<h3 className="subhead">Trees</h3>
				<Table rows={byGroup('ds.trees')} />
				<h3 className="subhead">Stacks</h3>
				<Table rows={byGroup('ds.stacks')} />
				<h3 className="subhead">Queues</h3>
				<Table rows={byGroup('ds.queues')} />
				<h3 className="subhead">Hash Tables</h3>
				<Table rows={byGroup('ds.hash')} />
				<h3 className="subhead">Heaps</h3>
				<Table rows={byGroup('ds.heap')} />
			</Section>
		</div>
	);
};

export default Topics;