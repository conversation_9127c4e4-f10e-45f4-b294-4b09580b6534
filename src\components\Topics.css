.accordion {
  max-width: 1200px;
  margin: 0 auto;
}

.acc-item {
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.acc-header {
  width: 100%;
  padding: 20px 24px;
  background: #f8fafc;
  border: none;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.2s ease;
}

.acc-header:hover {
  background: #f1f5f9;
}

.chev {
  font-size: 16px;
  transition: transform 0.2s ease;
  color: #64748b;
}

.chev.open {
  transform: rotate(90deg);
}

.acc-title {
  flex: 1;
  text-align: left;
}

.acc-body {
  padding: 24px;
}

.subhead {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #475569;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.subhead:not(:first-child) {
  margin-top: 32px;
}

.table-wrap {
  overflow-x: auto;
  margin-bottom: 24px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.q-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.q-table th {
  background: #f8fafc;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
}

.q-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.q-table tr:hover {
  background: #f8fafc;
}

.q-table tr:last-child td {
  border-bottom: none;
}

.chk {
  width: 40px;
  text-align: center;
}

.chk input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.level-easy {
  background: #dcfce7;
  color: #166534;
}

.level-medium {
  background: #fef3c7;
  color: #92400e;
}

.level-hard {
  background: #fecaca;
  color: #991b1b;
}

.q-table a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
}

.q-table a:hover {
  text-decoration: underline;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status.done {
  background: #dcfce7;
  color: #166534;
}

.status.pending {
  background: #f3f4f6;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
  .acc-header {
    padding: 16px 20px;
    font-size: 16px;
  }

  .acc-body {
    padding: 20px;
  }

  .q-table {
    font-size: 12px;
  }

  .q-table th,
  .q-table td {
    padding: 8px 12px;
  }

  .subhead {
    font-size: 14px;
  }
}