.accordion { max-width: 1000px; margin: 0 auto; }

.acc-item {
  margin-bottom: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.acc-header {
  width: 100%;
  padding: 16px;
  background: #f5f5f5;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  text-align: left;
}

.acc-header:hover { background: #eee; }

.chev { margin-right: 8px; transition: transform 0.2s; }
.chev.open { transform: rotate(90deg); }

.acc-body { padding: 16px; }

.subhead {
  margin: 16px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #555;
  border-bottom: 1px solid #ddd;
  padding-bottom: 4px;
}

.q-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin-bottom: 16px;
}

.q-table th, .q-table td {
  padding: 8px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.q-table th { background: #f9f9f9; font-weight: 600; }
.q-table tr:hover { background: #f5f5f5; }

.level {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.level-easy { background: #d4edda; color: #155724; }
.level-medium { background: #fff3cd; color: #856404; }
.level-hard { background: #f8d7da; color: #721c24; }

.q-table a { color: #007bff; text-decoration: none; margin-right: 8px; }
.q-table a:hover { text-decoration: underline; }

.status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.status.done { background: #d4edda; color: #155724; }
.status.pending { background: #f8f9fa; color: #6c757d; }