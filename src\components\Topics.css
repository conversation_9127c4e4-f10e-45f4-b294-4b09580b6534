.topics-simple {
  max-width: 800px;
  margin: 0 auto;
}

.topics-simple h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #333;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.topic-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.topic-header input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.topic-title {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.topic-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.topic-level.easy {
  background: #dcfce7;
  color: #166534;
}

.topic-level.medium {
  background: #fef3c7;
  color: #92400e;
}

.topic-level.hard {
  background: #fecaca;
  color: #991b1b;
}

.topic-links {
  display: flex;
  gap: 12px;
}

.topic-links a {
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  padding: 4px 8px;
  border: 1px solid #007bff;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.topic-links a:hover {
  background: #007bff;
  color: white;
}

.topics-note {
  margin-top: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}