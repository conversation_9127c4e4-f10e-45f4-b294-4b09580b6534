import React, { useState, useMemo } from 'react';
import './Dashboard.css';
import Topics from './Topics';
import { topics } from './topicsData';

const Dashboard = ({ user, onLogout }) => {
	const [active, setActive] = useState('profile');
	const [progressTick, setProgressTick] = useState(0);

	console.log('Dashboard render - active tab:', active); // Debug log
	const storageKey = useMemo(() => `apnacollege_progress_${user?.email || 'guest'}`, [user?.email]);

	const progress = useMemo(() => {
		try {
			const saved = localStorage.getItem(storageKey);
			const map = saved ? JSON.parse(saved) : {};
			const all = topics.length;
			const done = topics.filter(t => map[t.id]).length;
			const totalsByLevel = { Easy: 0, Medium: 0, Hard: 0 };
			const doneByLevel = { Easy: 0, Medium: 0, Hard: 0 };

			topics.forEach(t => {
				totalsByLevel[t.level]++;
				if (map[t.id]) doneByLevel[t.level]++;
			});

			return { done, all, totalsByLevel, doneByLevel };
		} catch {
			return {
				done: 0,
				all: 0,
				totalsByLevel: { Easy: 0, Medium: 0, Hard: 0 },
				doneByLevel: { Easy: 0, Medium: 0, Hard: 0 }
			};
		}
	}, [storageKey, progressTick]); // eslint-disable-line react-hooks/exhaustive-deps

	const NavItem = ({ id, icon, label }) => {
		const handleClick = (e) => {
			e.preventDefault();
			e.stopPropagation();
			console.log('NavItem clicked:', id, 'Current active:', active); // Debug log
			setActive(id);
		};

		return (
			<button
				className={`nav-item ${active === id ? 'active' : ''}`}
				onClick={handleClick}
				aria-pressed={active === id}
				type="button"
				data-tab-id={id}
				title={`Click to view ${label}`}
				style={{
					border: active === id ? '2px solid red' : '1px solid blue',
					margin: '2px'
				}}
			>
				<span className="icon" aria-hidden="true">{icon}</span>
				<span className="label">{label}</span>
			</button>
		);
	};

	const pct = (d, t) => (t ? Math.round((d / t) * 100) : 0);

	const ProgressBar = ({ done, total, color }) => (
		<div className="progress-bar">
			<div className="progress-fill" style={{ width: `${pct(done, total)}%`, backgroundColor: color }} />
		</div>
	);

	const StatCard = ({ title, value, subtitle, color }) => (
		<div className="stat-card">
			<div className="stat-value" style={{ color }}>{value}</div>
			<div className="stat-title">{title}</div>
			<div className="stat-subtitle">{subtitle}</div>
		</div>
	);

	return (
		<div className="dashboard">
			<header className="navbar">
				<div className="brand">
					<span className="brand-icon">🚀</span>
					<span className="brand-text">ApnaCollege DSA</span>
				</div>
				<nav className="nav">
					<NavItem id="profile" icon="👤" label="Profile" />
					<NavItem id="topics" icon="📚" label="Topics" />
					<NavItem id="progress" icon="📈" label="Progress" />
				</nav>
				<div className="spacer" />
				<div className="user-area">
					<span className="user-email">{user?.email}</span>
					<button className="logout" onClick={onLogout} title="Logout">🚪 Logout</button>
				</div>
			</header>

			<main className="content">
				<div style={{padding: '10px', background: '#e0e7ff', marginBottom: '10px', borderRadius: '4px'}}>
					<strong>Current Tab: {active}</strong>
				</div>
				{active === 'profile' && (
					<div className="panel">
						<div className="profile-header">
							<h2>Welcome back, {user?.name || 'User'}! 👋</h2>
							<p>Track your DSA learning progress and master algorithms</p>
						</div>

						<div className="stats-grid">
							<StatCard
								title="Total Problems"
								value={progress.all}
								subtitle="Available to solve"
								color="#4f46e5"
							/>
							<StatCard
								title="Completed"
								value={progress.done}
								subtitle={`${pct(progress.done, progress.all)}% done`}
								color="#10b981"
							/>
							<StatCard
								title="Easy Problems"
								value={progress.doneByLevel.Easy}
								subtitle={`of ${progress.totalsByLevel.Easy} completed`}
								color="#f59e0b"
							/>
							<StatCard
								title="Remaining"
								value={progress.all - progress.done}
								subtitle="problems to go"
								color="#ef4444"
							/>
						</div>
					</div>
				)}

				{active === 'topics' && (
					<div className="panel">
						<div className="panel-header">
							<h2>📚 DSA Topics & Problems</h2>
							<p>Click on topics to expand and mark problems as completed</p>
						</div>
						<Topics userEmail={user?.email} onProgressChange={() => setProgressTick(t => t + 1)} />
					</div>
				)}

				{active === 'progress' && (
					<div className="panel">
						<div className="panel-header">
							<h2>📈 Progress Overview</h2>
							<p>Track your learning journey across different difficulty levels</p>
						</div>

						<div className="progress-overview">
							<div className="overall-progress">
								<h3>Overall Progress</h3>
								<div className="progress-stats">
									<span className="progress-numbers">{progress.done} / {progress.all}</span>
									<span className="progress-percentage">({pct(progress.done, progress.all)}%)</span>
								</div>
								<ProgressBar done={progress.done} total={progress.all} color="#4f46e5" />
							</div>

							<div className="level-progress">
								<h3>Progress by Difficulty</h3>
								<div className="level-simple">
									<div className="level-row">
										<span className="level-name easy">Easy</span>
										<span className="level-count">{progress.doneByLevel.Easy} / {progress.totalsByLevel.Easy}</span>
										<span className="level-percentage">{pct(progress.doneByLevel.Easy, progress.totalsByLevel.Easy)}%</span>
									</div>

									<div className="level-row">
										<span className="level-name medium">Medium</span>
										<span className="level-count">{progress.doneByLevel.Medium} / {progress.totalsByLevel.Medium}</span>
										<span className="level-percentage">{pct(progress.doneByLevel.Medium, progress.totalsByLevel.Medium)}%</span>
									</div>

									<div className="level-row">
										<span className="level-name hard">Hard</span>
										<span className="level-count">{progress.doneByLevel.Hard} / {progress.totalsByLevel.Hard}</span>
										<span className="level-percentage">{pct(progress.doneByLevel.Hard, progress.totalsByLevel.Hard)}%</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</main>
		</div>
	);
};

export default Dashboard;