import { useState } from 'react';
import './Dashboard.css';
import Topics from './Topics';
import { topics } from './topicsData';

const Dashboard = ({ user, onLogout }) => {
	const [active, setActive] = useState('profile');



	const getProgress = () => {
		const saved = localStorage.getItem(`progress_${user?.name || 'guest'}`);
		const done = saved ? Object.keys(JSON.parse(saved)).filter(k => JSON.parse(saved)[k]).length : 0;
		return { done, all: topics.length };
	};

	const { done, all } = getProgress();
	const pct = all ? Math.round((done / all) * 100) : 0;

	const Nav = ({ id, icon, label }) => (
		<button className={`nav-item ${active === id ? 'active' : ''}`} onClick={() => setActive(id)}>
			{icon} {label}
		</button>
	);

	const Card = ({ title, value, subtitle }) => (
		<div className="stat-card">
			<div className="stat-value">{value}</div>
			<div className="stat-title">{title}</div>
			<div className="stat-subtitle">{subtitle}</div>
		</div>
	);

	return (
		<div className="dashboard">
			<header className="navbar">
				<div className="brand">🚀 ApnaCollege DSA</div>
				<nav className="nav">
					<Nav id="profile" icon="👤" label="Profile" />
					<Nav id="topics" icon="📚" label="Topics" />
					<Nav id="progress" icon="📈" label="Progress" />
				</nav>
				<div className="spacer" />
				<div className="user-area">
					<span>{user?.name}</span>
					<button className="logout" onClick={onLogout}>Logout</button>
				</div>
			</header>

			<main className="content">
				{active === 'profile' && (
					<div className="panel">
						<h2>Welcome, {user?.name}! 👋</h2>
						<div className="stats-grid">
							<Card title="Total Problems" value={all} subtitle="Available" />
							<Card title="Completed" value={done} subtitle={`${pct}% done`} />
							<Card title="Remaining" value={all - done} subtitle="to go" />
						</div>
					</div>
				)}

				{active === 'topics' && (
					<div className="panel">
						<h2>📚 DSA Topics</h2>
						<Topics userEmail={user?.name} />
					</div>
				)}

				{active === 'progress' && (
					<div className="panel">
						<h2>📈 Progress</h2>
						<p>Completed {done} of {all} problems ({pct}%)</p>
						<div className="progress-bar">
							<div className="progress-fill" style={{ width: `${pct}%`, backgroundColor: '#4f46e5' }} />
						</div>
					</div>
				)}
			</main>
		</div>
	);
};

export default Dashboard;