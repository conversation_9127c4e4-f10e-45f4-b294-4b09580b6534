import React, { useState, useEffect } from 'react';
import './App.css';
import LoginForm from './components/LoginForm';
import Dashboard from './components/Dashboard';

function App() {
  const [user, setUser] = useState(null);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('apnacollege_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error loading user from localStorage:', error);
        localStorage.removeItem('apnacollege_user');
      }
    }
  }, []);

  // Handle login - save user to localStorage
  const handleLogin = (userData) => {
    setUser(userData);
    localStorage.setItem('apnacollege_user', JSON.stringify(userData));
  };

  // Handle logout - remove user from localStorage
  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('apnacollege_user');
  };

  return (
    <div className="App">
      {!user ? (
        <LoginForm onLogin={handleLogin} />
      ) : (
        <Dashboard user={user} onLogout={handleLogout} />
      )}
    </div>
  );
}

export default App;
